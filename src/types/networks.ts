export abstract class Network {
  name: string;
  fullName: string;
  logoUrl: string;
  chainId: number;
  rpcUrl: string;
  explorerURL: string;
  explorerName: string;

  constructor(config: {
    name: string;
    fullName: string;
    chainId: number;
    logoUrl: string;
    rpcUrl: string;
    explorerURL: string;
    explorerName: string;
  }) {
    this.name = config.name;
    this.fullName = config.fullName;
    this.chainId = config.chainId;
    this.logoUrl = config.logoUrl;
    this.rpcUrl = config.rpcUrl;
    this.explorerURL = config.explorerURL;
    this.explorerName = config.explorerName;
  }
}
