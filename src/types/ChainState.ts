import { makeAutoObservable } from 'mobx';
import { NetworkState } from './NetworkState';
import { TokenState } from './TokenState';
import { CCSwapTokensPairs, CrossChain } from '../../../type';
import { _ } from '@/lib/lodash';
import { clusterApiUrl } from '@solana/web3.js';
import * as chai from 'chai';
import { IotexMainnetConfig } from '../../config/IotexMainnetConfig';

export class ChainState {
  name: string;
  fullName: string;
  alias?: string;
  logoUrl: string;
  network: NetworkState;
  isPaused: boolean = false;
  chainId: number;
  rpcUrl: string;
  explorerName: string;
  explorerURL: string;
  Coin: TokenState;
  label?: string = '';
  confirmationTimes: number = 12;
  nativeCurrency: TokenState;
  info: {
    blockPerSeconds: number;
    multicallAddr?: string;
  };
  ccSwapRouter?: string;
  clusterApiUrl?: string[];
  ccSwapTokensPairs: Partial<CCSwapTokensPairs>;
  crossChain: {
    [key: number]: Partial<CrossChain>;
  };

  constructor(args: Partial<ChainState>) {
    // this.crossChain.current
    // this.crossChain.setCurrentId()
    Object.assign(this, args);
    makeAutoObservable(this, { network: false });
  }
  init() {
    this.Coin.network = this.network;
    _.each(this.crossChain, (v, k) => {
      _.each(v.tokens, (token) => {
        token.network = this.network;
      });
    });
  }

  getCrossChain(chainId: number) {
    let result = this.crossChain[chainId]
    if (result) return result;
    result = this.crossChain[4689]
    if (result) return result;
    return this.crossChain[Object.keys(this.crossChain)[0]];
  }
}
